<?php

namespace App\Filament\Pages\Auth;

use Filament\Pages\Page;
use App\Models\shops;
use App\Models\subscription;
use App\Models\User;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;


class Register extends BaseRegister
{
    protected function handleRegistration(array $data): User
    {
        return DB::transaction(function () use ($data) {
            $plainPassword = $data['password'];

            // Only hash if it doesn't look like a bcrypt hash
            if (!str_starts_with($plainPassword, '$2y$')) {
                $plainPassword = Hash::make($plainPassword);
            }

            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => $plainPassword,
                'role' => 'Admin',
            ]);
            shops::create([
                'user_id' => $user->id,
            ]);

            subscription::create([
                'user_id' => $user->id,
            ]);

            return $user;
        });
    }
}
